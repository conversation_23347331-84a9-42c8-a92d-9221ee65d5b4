<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { NCard, NGrid, NGridItem, NStatistic, NTag, NSpin, NAlert } from 'naive-ui';
import { fetchAppHealth } from '@/service/api';
import { useEcharts } from '@/hooks/common/echarts';

// 应用健康状态数据
const loading = ref(true);
const error = ref<string | null>(null);
const healthData = ref<Api.Monitor.AppHealthData | null>(null);

// 应用健康状态仪表盘
const { domRef: healthGaugeRef, updateOptions: updateHealthGauge } = useEcharts(() => ({
  title: {
    text: '应用健康状态',
    left: 'center',
    fontSize: 16,
    fontWeight: 'bold'
  },
  series: [{
    name: '健康状态',
    type: 'gauge',
    center: ['50%', '60%'],
    startAngle: 200,
    endAngle: -20,
    min: 0,
    max: 100,
    splitNumber: 4,
    itemStyle: {
      color: '#52c41a',
      shadowColor: 'rgba(82, 196, 26, 0.45)',
      shadowBlur: 10,
      shadowOffsetX: 2,
      shadowOffsetY: 2
    },
    progress: {
      show: true,
      roundCap: true,
      width: 20
    },
    pointer: {
      length: '75%',
      width: 18,
      offsetCenter: [0, '5%']
    },
    axisLine: {
      roundCap: true,
      lineStyle: {
        width: 20
      }
    },
    axisTick: {
      splitNumber: 2,
      lineStyle: {
        width: 2,
        color: '#999'
      }
    },
    splitLine: {
      length: 15,
      lineStyle: {
        width: 3,
        color: '#999'
      }
    },
    axisLabel: {
      distance: 35,
      color: '#999',
      fontSize: 12,
      formatter: function(value: number) {
        if (value === 0) return '故障';
        if (value === 25) return '警告';
        if (value === 50) return '一般';
        if (value === 75) return '良好';
        if (value === 100) return '优秀';
        return '';
      }
    },
    title: {
      show: false
    },
    detail: {
      backgroundColor: '#fff',
      borderColor: '#999',
      borderWidth: 2,
      width: '60%',
      lineHeight: 40,
      height: 40,
      borderRadius: 8,
      offsetCenter: [0, '35%'],
      valueAnimation: true,
      formatter: function (value: number) {
        let status = '故障';
        let color = '#ff4d4f';
        if (value >= 80) {
          status = '健康';
          color = '#52c41a';
        } else if (value >= 60) {
          status = '良好';
          color = '#1890ff';
        } else if (value >= 40) {
          status = '警告';
          color = '#faad14';
        }
        return `{status|${status}}`;
      },
      rich: {
        status: {
          fontSize: 16,
          fontWeight: 'bolder',
          color: '#333'
        }
      }
    },
    data: [{ value: 0 }]
  }]
}));

// 组件状态分布饼图
const { domRef: componentStatusRef, updateOptions: updateComponentStatus } = useEcharts(() => ({
  title: {
    text: '组件状态分布',
    left: 'center',
    fontSize: 14
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    bottom: '5%',
    left: 'center'
  },
  series: [{
    name: '组件状态',
    type: 'pie',
    radius: ['40%', '70%'],
    center: ['50%', '45%'],
    data: [] as Array<{ name: string; value: number; itemStyle: { color: string } }>,
    itemStyle: {
      borderRadius: 8,
      borderColor: '#fff',
      borderWidth: 2
    },
    label: {
      show: false
    },
    emphasis: {
      label: {
        show: true,
        fontSize: '12',
        fontWeight: 'bold'
      }
    }
  }]
}));

onMounted(async () => {
  await loadHealthData();
});

async function loadHealthData() {
  try {
    loading.value = true;
    error.value = null;

    // 调用监控API获取应用健康状态
    const { data } = await fetchAppHealth();
    healthData.value = data;

    // 更新图表数据
    updateCharts();
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载健康数据失败';
    console.error('加载应用健康数据失败:', err);
  } finally {
    loading.value = false;
  }
}

// 监听数据变化，更新图表
watch(healthData, () => {
  updateCharts();
}, { deep: true });

function updateCharts() {
  if (!healthData.value) return;

  try {
    // 计算整体健康分数
    const healthScore = calculateHealthScore();

    // 验证健康分数有效性
    if (typeof healthScore === 'number' && healthScore >= 0 && healthScore <= 100) {
      // 更新健康状态仪表盘
      updateHealthGauge(prev => ({
        ...prev,
        series: [{
          ...prev.series[0],
          data: [{ value: healthScore }],
          itemStyle: {
            ...prev.series[0].itemStyle,
            color: getHealthColor(healthScore)
          }
        }]
      }));
    }

    // 更新组件状态分布饼图
    if (healthData.value.database && healthData.value.strm_tasks && healthData.value.api_performance) {
      const componentData = [
        {
          name: '数据库',
          value: healthData.value.database.status === 'connected' ? 1 : 0,
          itemStyle: { color: healthData.value.database.status === 'connected' ? '#52c41a' : '#ff4d4f' }
        },
        {
          name: 'STRM任务',
          value: 1,
          itemStyle: { color: getStatusColor(healthData.value.strm_tasks.status) }
        },
        {
          name: 'API性能',
          value: 1,
          itemStyle: { color: getStatusColor(healthData.value.api_performance.status) }
        }
      ];

      updateComponentStatus(prev => ({
        ...prev,
        series: [{ ...prev.series[0], data: componentData }]
      }));
    }
  } catch (error) {
    console.error('Error updating health charts:', error);
  }
}

function calculateHealthScore(): number {
  if (!healthData.value) return 0;

  let score = 0;
  let components = 0;

  // 数据库状态 (30%)
  if (healthData.value.database.status === 'connected') {
    score += 30;
  }
  components++;

  // STRM任务状态 (35%)
  const strmStatus = healthData.value.strm_tasks.status;
  if (strmStatus === 'healthy') score += 35;
  else if (strmStatus === 'warning') score += 20;
  components++;

  // API性能状态 (35%)
  const apiStatus = healthData.value.api_performance.status;
  if (apiStatus === 'healthy') score += 35;
  else if (apiStatus === 'warning') score += 20;
  components++;

  return Math.min(100, score);
}

function getHealthColor(score: number): string {
  if (score >= 80) return '#52c41a';
  if (score >= 60) return '#1890ff';
  if (score >= 40) return '#faad14';
  return '#ff4d4f';
}

function getStatusColor(status: string): string {
  switch (status) {
    case 'healthy': return '#52c41a';
    case 'warning': return '#faad14';
    case 'error': return '#ff4d4f';
    default: return '#d9d9d9';
  }
}

// 外部数据更新方法
function updateData(newData: Api.Monitor.AppHealthData) {
  healthData.value = newData;
  error.value = null;
}

// 暴露方法供父组件调用
defineExpose({
  loadHealthData,
  updateData
});

function getStatusType(status: string) {
  switch (status) {
    case 'healthy':
      return 'success';
    case 'warning':
      return 'warning';
    case 'error':
      return 'error';
    default:
      return 'default';
  }
}

function formatUptime(seconds: number) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${days}天 ${hours}小时 ${minutes}分钟`;
}

function formatBytes(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>

<template>
  <NCard title="应用健康状态" class="h-full">
    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" closable @close="error = null" />
      </div>

      <div v-if="healthData" class="space-y-6">
        <!-- 健康状态仪表盘 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">健康状态概览</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <!-- 整体健康状态仪表盘 -->
            <NGridItem>
              <NCard size="small" class="chart-card">
                <div ref="healthGaugeRef" class="chart-container"></div>
              </NCard>
            </NGridItem>

            <!-- 组件状态分布 -->
            <NGridItem>
              <NCard size="small" class="chart-card">
                <div ref="componentStatusRef" class="chart-container"></div>
              </NCard>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 应用基本信息 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">应用信息</h3>
          <NGrid :cols="4" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic label="应用名称" :value="healthData.app_info.name" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="版本" :value="healthData.app_info.version" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="运行时长" :value="formatUptime(healthData.app_info.uptime)" />
            </NGridItem>
            <NGridItem>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">状态:</span>
                <NTag :type="getStatusType(healthData.app_info.status)">
                  {{ healthData.app_info.status === 'healthy' ? '健康' :
                     healthData.app_info.status === 'warning' ? '警告' : '错误' }}
                </NTag>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 组件状态 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">组件状态</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <!-- 数据库状态 -->
            <NGridItem>
              <NCard size="small" title="数据库">
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span>状态:</span>
                    <NTag :type="getStatusType(healthData.database.status === 'connected' ? 'healthy' : 'error')">
                      {{ healthData.database.status === 'connected' ? '已连接' : '断开' }}
                    </NTag>
                  </div>
                  <div class="flex justify-between">
                    <span>大小:</span>
                    <span>{{ formatBytes(healthData.database.size) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>连接池:</span>
                    <span>{{ healthData.database.connection_pool.active }}/{{ healthData.database.connection_pool.total }}</span>
                  </div>
                </div>
              </NCard>
            </NGridItem>

            <!-- STRM任务状态 -->
            <NGridItem>
              <NCard size="small" title="STRM任务">
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span>状态:</span>
                    <NTag :type="getStatusType(healthData.strm_tasks.status)">
                      {{ healthData.strm_tasks.status === 'healthy' ? '健康' :
                         healthData.strm_tasks.status === 'warning' ? '警告' : '错误' }}
                    </NTag>
                  </div>
                  <div class="flex justify-between">
                    <span>总任务:</span>
                    <span>{{ healthData.strm_tasks.total_tasks }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>运行中:</span>
                    <span>{{ healthData.strm_tasks.running_tasks }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>成功率:</span>
                    <span>{{ (healthData.strm_tasks.success_rate * 100).toFixed(1) }}%</span>
                  </div>
                </div>
              </NCard>
            </NGridItem>

            <!-- API性能状态 -->
            <NGridItem>
              <NCard size="small" title="API性能">
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span>状态:</span>
                    <NTag :type="getStatusType(healthData.api_performance.status)">
                      {{ healthData.api_performance.status === 'healthy' ? '健康' :
                         healthData.api_performance.status === 'warning' ? '警告' : '错误' }}
                    </NTag>
                  </div>
                  <div class="flex justify-between">
                    <span>平均响应:</span>
                    <span>{{ healthData.api_performance.avg_response_time.toFixed(2) }}s</span>
                  </div>
                  <div class="flex justify-between">
                    <span>24h请求:</span>
                    <span>{{ healthData.api_performance.request_count_24h }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>错误率:</span>
                    <span>{{ (healthData.api_performance.error_rate * 100).toFixed(2) }}%</span>
                  </div>
                </div>
              </NCard>
            </NGridItem>
          </NGrid>
        </div>
      </div>

      <div v-else-if="!loading" class="text-center text-gray-500 py-8">
        暂无健康状态数据
      </div>
    </NSpin>
  </NCard>
</template>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.h-full {
  height: 100%;
}

.chart-card {
  height: 300px;
}

.chart-container {
  width: 100%;
  height: 260px;
}
</style>
