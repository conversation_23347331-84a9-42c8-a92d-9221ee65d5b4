<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { NCard, NGrid, NGridItem, NSpin, NAlert, NStatistic } from 'naive-ui';
import { fetchPerformanceData } from '@/service/api';
import { useEcharts } from '@/hooks/common/echarts';

// 性能监控数据
const loading = ref(true);
const error = ref<string | null>(null);
const performanceData = ref<Api.Monitor.PerformanceData | null>(null);

// 响应时间趋势图
const { domRef: responseTimeChartRef, updateOptions: updateResponseTimeChart } = useEcharts(() => ({
  title: {
    text: 'API响应时间趋势',
    left: 'center',
    fontSize: 14
  },
  tooltip: {
    trigger: 'axis',
    formatter: '{b}: {c}ms'
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    name: '响应时间(ms)',
    axisLabel: {
      formatter: '{value}ms'
    }
  },
  series: [{
    name: '响应时间',
    type: 'line',
    smooth: true,
    data: [] as number[],
    lineStyle: {
      color: '#5da8ff',
      width: 3
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: 'rgba(93, 168, 255, 0.3)' },
          { offset: 1, color: 'rgba(93, 168, 255, 0.1)' }
        ]
      }
    }
  }]
}));

// 请求量柱状图
const { domRef: requestCountChartRef, updateOptions: updateRequestCountChart } = useEcharts(() => ({
  title: {
    text: '请求量统计',
    left: 'center',
    fontSize: 14
  },
  tooltip: {
    trigger: 'axis',
    formatter: '{b}: {c}次'
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    name: '请求次数'
  },
  series: [{
    name: '请求量',
    type: 'bar',
    data: [] as number[],
    itemStyle: {
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#26deca' },
          { offset: 1, color: '#26deca80' }
        ]
      }
    }
  }]
}));

// 状态码分布饼图
const { domRef: statusCodeChartRef, updateOptions: updateStatusCodeChart } = useEcharts(() => ({
  title: {
    text: 'HTTP状态码分布',
    left: 'center',
    fontSize: 14
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    bottom: '5%',
    left: 'center'
  },
  series: [{
    name: '状态码',
    type: 'pie',
    radius: ['40%', '70%'],
    center: ['50%', '45%'],
    data: [] as Array<{ name: string; value: number }>,
    itemStyle: {
      borderRadius: 8,
      borderColor: '#fff',
      borderWidth: 2
    },
    label: {
      show: false
    },
    emphasis: {
      label: {
        show: true,
        fontSize: '12',
        fontWeight: 'bold'
      }
    }
  }]
}));

onMounted(async () => {
  await loadPerformanceData();
});

async function loadPerformanceData() {
  try {
    loading.value = true;
    error.value = null;

    // 调用监控API获取性能数据
    const { data } = await fetchPerformanceData(60); // 获取最近60分钟的数据
    performanceData.value = data;

    // 更新图表数据
    updateCharts();
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载性能数据失败';
    console.error('加载性能监控数据失败:', err);
  } finally {
    loading.value = false;
  }
}

// 监听数据变化，更新图表
watch(performanceData, () => {
  updateCharts();
}, { deep: true });

function updateCharts() {
  if (!performanceData.value) return;

  // 模拟时间序列数据（实际应该从API获取）
  const timeLabels = Array.from({ length: 12 }, (_, i) => {
    const time = new Date();
    time.setMinutes(time.getMinutes() - (11 - i) * 5);
    return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  });

  // 更新响应时间趋势图
  const responseTimeData = Array.from({ length: 12 }, () =>
    Math.random() * 500 + performanceData.value!.api_performance.avg_response_time * 1000
  );

  updateResponseTimeChart(prev => ({
    ...prev,
    xAxis: { ...prev.xAxis, data: timeLabels },
    series: [{ ...prev.series[0], data: responseTimeData }]
  }));

  // 更新请求量柱状图
  const requestData = Array.from({ length: 12 }, () => Math.floor(Math.random() * 100 + 50));

  updateRequestCountChart(prev => ({
    ...prev,
    xAxis: { ...prev.xAxis, data: timeLabels },
    series: [{ ...prev.series[0], data: requestData }]
  }));

  // 更新状态码分布饼图
  const statusCodeData = Object.entries(performanceData.value.api_performance.status_code_distribution || {})
    .map(([code, count]) => ({
      name: `${code}状态码`,
      value: count
    }));

  updateStatusCodeChart(prev => ({
    ...prev,
    series: [{ ...prev.series[0], data: statusCodeData }]
  }));
}

// 外部数据更新方法
function updateData(newData: Api.Monitor.PerformanceData) {
  performanceData.value = newData;
  error.value = null;
}

// 暴露方法供父组件调用
defineExpose({
  loadPerformanceData,
  updateData
});

function formatBytes(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>

<template>
  <NCard title="性能监控" class="h-full">
    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" closable @close="error = null" />
      </div>

      <div v-if="performanceData" class="space-y-6">
        <!-- API性能概览 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">API性能概览</h3>
          <NGrid :cols="4" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic
                label="平均响应时间"
                :value="`${performanceData.api_performance.avg_response_time.toFixed(2)}s`"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic label="总请求数" :value="performanceData.api_performance.request_count" />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="错误率"
                :value="`${(performanceData.api_performance.error_rate * 100).toFixed(2)}%`"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="每分钟请求"
                :value="performanceData.api_performance.requests_per_minute.toFixed(1)"
              />
            </NGridItem>
          </NGrid>
        </div>

        <!-- 性能图表 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">性能趋势图表</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <!-- 响应时间趋势图 -->
            <NGridItem>
              <NCard size="small" class="chart-card">
                <div ref="responseTimeChartRef" class="chart-container"></div>
              </NCard>
            </NGridItem>

            <!-- 请求量统计图 -->
            <NGridItem>
              <NCard size="small" class="chart-card">
                <div ref="requestCountChartRef" class="chart-container"></div>
              </NCard>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 状态码分布图 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">状态码分布</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NCard size="small" class="chart-card">
                <div ref="statusCodeChartRef" class="chart-container"></div>
              </NCard>
            </NGridItem>

            <!-- 状态码详情 -->
            <NGridItem>
              <div class="space-y-2">
                <div
                  v-for="(count, code) in performanceData.api_performance.status_code_distribution"
                  :key="code"
                  class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                >
                  <div class="flex items-center space-x-2">
                    <div
                      class="w-3 h-3 rounded-full"
                      :class="{
                        'bg-green-500': code.startsWith('2'),
                        'bg-blue-500': code.startsWith('3'),
                        'bg-yellow-500': code.startsWith('4'),
                        'bg-red-500': code.startsWith('5')
                      }"
                    ></div>
                    <span class="text-sm font-medium">{{ code }}状态码</span>
                  </div>
                  <span class="text-lg font-bold">{{ count }}</span>
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 最慢端点 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">最慢API端点</h3>
          <div class="space-y-2">
            <div
              v-for="(endpoint, index) in performanceData.api_performance.slowest_endpoints"
              :key="index"
              class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
            >
              <div class="flex-1">
                <div class="text-sm font-mono">{{ endpoint.endpoint }}</div>
                <div class="text-xs text-gray-500">请求次数: {{ endpoint.count }}</div>
              </div>
              <div class="text-right">
                <div class="text-sm font-semibold">平均: {{ endpoint.avg_time.toFixed(2) }}s</div>
                <div class="text-xs text-gray-500">最大: {{ endpoint.max_time.toFixed(2) }}s</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 状态码分布 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">HTTP状态码分布</h3>
          <NGrid :cols="6" :x-gap="16" :y-gap="16">
            <NGridItem
              v-for="(count, code) in performanceData.api_performance.status_code_distribution"
              :key="code"
            >
              <div class="text-center p-3 border rounded-lg">
                <div class="text-2xl font-bold" :class="{
                  'text-green-600': code.startsWith('2'),
                  'text-blue-600': code.startsWith('3'),
                  'text-yellow-600': code.startsWith('4'),
                  'text-red-600': code.startsWith('5')
                }">
                  {{ count }}
                </div>
                <div class="text-sm text-gray-600">{{ code }}</div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 数据库性能 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">数据库性能</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic
                label="平均查询时间"
                :value="`${performanceData.database_performance.query_stats.avg_query_time.toFixed(2)}s`"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic label="慢查询数" :value="performanceData.database_performance.query_stats.slow_queries" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="总查询数" :value="performanceData.database_performance.query_stats.total_queries" />
            </NGridItem>
          </NGrid>
        </div>

        <!-- 系统资源 -->
        <div v-if="performanceData.system_resources.available">
          <h3 class="text-lg font-semibold mb-4">系统资源使用</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="text-center p-4 border rounded-lg">
                <div class="text-3xl font-bold mb-2" :class="{
                  'text-green-600': performanceData.system_resources.memory_usage < 50,
                  'text-yellow-600': performanceData.system_resources.memory_usage >= 50 && performanceData.system_resources.memory_usage < 80,
                  'text-red-600': performanceData.system_resources.memory_usage >= 80
                }">
                  {{ performanceData.system_resources.memory_usage.toFixed(1) }}%
                </div>
                <div class="text-sm text-gray-600 mb-1">内存使用率</div>
                <div class="text-xs text-gray-500">
                  {{ formatBytes(performanceData.system_resources.memory_total - performanceData.system_resources.memory_available) }} /
                  {{ formatBytes(performanceData.system_resources.memory_total) }}
                </div>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="text-center p-4 border rounded-lg">
                <div class="text-3xl font-bold mb-2" :class="{
                  'text-green-600': performanceData.system_resources.cpu_usage < 50,
                  'text-yellow-600': performanceData.system_resources.cpu_usage >= 50 && performanceData.system_resources.cpu_usage < 80,
                  'text-red-600': performanceData.system_resources.cpu_usage >= 80
                }">
                  {{ performanceData.system_resources.cpu_usage.toFixed(1) }}%
                </div>
                <div class="text-sm text-gray-600">CPU使用率</div>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="text-center p-4 border rounded-lg">
                <div class="text-3xl font-bold mb-2" :class="{
                  'text-green-600': performanceData.system_resources.disk_usage < 50,
                  'text-yellow-600': performanceData.system_resources.disk_usage >= 50 && performanceData.system_resources.disk_usage < 80,
                  'text-red-600': performanceData.system_resources.disk_usage >= 80
                }">
                  {{ performanceData.system_resources.disk_usage.toFixed(1) }}%
                </div>
                <div class="text-sm text-gray-600 mb-1">磁盘使用率</div>
                <div class="text-xs text-gray-500">
                  {{ formatBytes(performanceData.system_resources.disk_total - performanceData.system_resources.disk_free) }} /
                  {{ formatBytes(performanceData.system_resources.disk_total) }}
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>
      </div>

      <div v-else-if="!loading" class="text-center text-gray-500 py-8">
        暂无性能监控数据
      </div>
    </NSpin>
  </NCard>
</template>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.h-full {
  height: 100%;
}

.chart-card {
  height: 300px;
}

.chart-container {
  width: 100%;
  height: 260px;
}
</style>
